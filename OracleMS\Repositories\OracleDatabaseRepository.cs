using System.Data;
using Microsoft.Extensions.Logging;
using Oracle.ManagedDataAccess.Client;
using OracleMS.Exceptions;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.Repositories;

/// <summary>
/// Oracle 資料庫操作儲存庫
/// </summary>
public class OracleDatabaseRepository : IDatabaseRepository
{
    private readonly ILogger<OracleDatabaseRepository> _logger;

    public OracleDatabaseRepository(ILogger<OracleDatabaseRepository> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 取得資料庫物件清單
    /// </summary>
    public async Task<IEnumerable<DatabaseObject>> GetDatabaseObjectsAsync(IDbConnection connection, DatabaseObjectType type)
    {
        try
        {
            _logger.LogInformation("正在取得資料庫物件清單，類型: {ObjectType}", type);

            return type switch
            {
                DatabaseObjectType.Table => await GetTablesAsync(connection),
                DatabaseObjectType.View => await GetViewsAsync(connection),
                DatabaseObjectType.Procedure => await GetProceduresAsync(connection),
                DatabaseObjectType.Function => await GetFunctionsAsync(connection),
                DatabaseObjectType.Package => await GetPackagesAsync(connection),
                DatabaseObjectType.Sequence => await GetSequencesAsync(connection),
                DatabaseObjectType.Trigger => await GetTriggersAsync(connection),
                DatabaseObjectType.Index => await GetIndexesAsync(connection),
                _ => throw new ArgumentException($"不支援的物件類型: {type}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取得資料庫物件清單失敗，類型: {ObjectType}", type);
            throw new OracleManagementException($"取得資料庫物件清單失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 執行查詢並返回 DataTable
    /// </summary>
    public async Task<DataTable> ExecuteQueryAsync(IDbConnection connection, string sql)
    {
        try
        {
            _logger.LogInformation("正在執行查詢: {Sql}", sql.Length > 100 ? sql[..100] + "..." : sql);

            using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandTimeout = 300; // 5 分鐘逾時

            var dataTable = new DataTable();
            
            if (command is OracleCommand oracleCommand)
            {
                using var adapter = new OracleDataAdapter(oracleCommand);
                await Task.Run(() => adapter.Fill(dataTable));
            }
            else
            {
                // 備用方案：使用 DataReader
                using var reader = await Task.Run(() => command.ExecuteReader());
                dataTable.Load(reader);
            }

            _logger.LogInformation("查詢執行成功，返回 {RowCount} 筆資料", dataTable.Rows.Count);
            return dataTable;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "執行查詢失敗: {Sql}", sql);
            throw new OracleManagementException($"執行查詢失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 執行非查詢 SQL 語句
    /// </summary>
    public async Task<int> ExecuteNonQueryAsync(IDbConnection connection, string sql)
    {
        try
        {
            _logger.LogInformation("正在執行非查詢語句: {Sql}", sql.Length > 100 ? sql[..100] + "..." : sql);

            using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandTimeout = 300; // 5 分鐘逾時

            var rowsAffected = await Task.Run(() => command.ExecuteNonQuery());

            _logger.LogInformation("非查詢語句執行成功，影響 {RowsAffected} 筆資料", rowsAffected);
            return rowsAffected;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "執行非查詢語句失敗: {Sql}", sql);
            throw new OracleManagementException($"執行非查詢語句失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 取得資料表結構資訊
    /// </summary>
    public async Task<TableSchema> GetTableSchemaAsync(IDbConnection connection, string tableName)
    {
        try
        {
            _logger.LogInformation("正在取得資料表結構: {TableName}", tableName);

            var schema = new TableSchema
            {
                TableName = tableName,
                Owner = await GetTableOwnerAsync(connection, tableName)
            };

            // 並行取得各種資訊
            var columnsTask = GetColumnsAsync(connection, tableName);
            var indexesTask = GetIndexesAsync(connection, tableName);
            var constraintsTask = GetConstraintsAsync(connection, tableName);
            var triggersTask = GetTriggersAsync(connection, tableName);

            await Task.WhenAll(columnsTask, indexesTask, constraintsTask, triggersTask);
            
            schema.Columns = (await columnsTask).ToList();
            schema.Indexes = (await indexesTask).ToList();
            schema.Constraints = (await constraintsTask).ToList();
            schema.Triggers = (await triggersTask).ToList();

            _logger.LogInformation("資料表結構取得成功: {TableName}", tableName);
            return schema;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取得資料表結構失敗: {TableName}", tableName);
            throw new OracleManagementException($"取得資料表結構失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 取得資料表清單
    /// </summary>
    public async Task<IEnumerable<DatabaseObject>> GetTablesAsync(IDbConnection connection)
    {
        const string sql = @"
            SELECT
                o.object_name as Name,
                o.owner as Owner,
                o.created as CreatedDate,
                o.last_ddl_time as ModifiedDate,
                o.status as Status
            FROM all_objects o
            WHERE o.object_type = 'TABLE'
              AND o.owner = USER
            ORDER BY o.object_name";

        return await GetDatabaseObjectsFromQueryAsync(connection, sql, DatabaseObjectType.Table);
    }

    /// <summary>
    /// 取得檢視清單
    /// </summary>
    public async Task<IEnumerable<DatabaseObject>> GetViewsAsync(IDbConnection connection)
    {
        const string sql = @"
            SELECT
                o.object_name as Name,
                o.owner as Owner,
                o.created as CreatedDate,
                o.last_ddl_time as ModifiedDate,
                o.status as Status
            FROM all_objects o
            WHERE o.object_type = 'VIEW'
              AND o.owner = USER
            ORDER BY o.object_name";

        return await GetDatabaseObjectsFromQueryAsync(connection, sql, DatabaseObjectType.View);
    }

    /// <summary>
    /// 取得預存程序清單
    /// </summary>
    public async Task<IEnumerable<DatabaseObject>> GetProceduresAsync(IDbConnection connection)
    {
        const string sql = @"
            SELECT 
                object_name as Name,
                owner as Owner,
                created as CreatedDate,
                last_ddl_time as ModifiedDate,
                status as Status
            FROM all_objects 
            WHERE object_type = 'PROCEDURE' 
            AND owner = USER 
            ORDER BY object_name";

        return await GetDatabaseObjectsFromQueryAsync(connection, sql, DatabaseObjectType.Procedure);
    }

    /// <summary>
    /// 取得函數清單
    /// </summary>
    public async Task<IEnumerable<DatabaseObject>> GetFunctionsAsync(IDbConnection connection)
    {
        const string sql = @"
            SELECT 
                object_name as Name,
                owner as Owner,
                created as CreatedDate,
                last_ddl_time as ModifiedDate,
                status as Status
            FROM all_objects 
            WHERE object_type = 'FUNCTION' 
            AND owner = USER 
            ORDER BY object_name";

        return await GetDatabaseObjectsFromQueryAsync(connection, sql, DatabaseObjectType.Function);
    }
  
  /// <summary>
    /// 取得套件清單
    /// </summary>
    private async Task<IEnumerable<DatabaseObject>> GetPackagesAsync(IDbConnection connection)
    {
        const string sql = @"
            SELECT 
                object_name as Name,
                owner as Owner,
                created as CreatedDate,
                last_ddl_time as ModifiedDate,
                status as Status
            FROM all_objects 
            WHERE object_type = 'PACKAGE' 
            AND owner = USER 
            ORDER BY object_name";

        return await GetDatabaseObjectsFromQueryAsync(connection, sql, DatabaseObjectType.Package);
    }

    /// <summary>
    /// 取得序列清單
    /// </summary>
    private async Task<IEnumerable<DatabaseObject>> GetSequencesAsync(IDbConnection connection)
    {
        const string sql = @"
            SELECT
                o.object_name as Name,
                o.owner as Owner,
                o.created as CreatedDate,
                o.last_ddl_time as ModifiedDate,
                o.status as Status
            FROM all_objects o
            WHERE o.object_type = 'SEQUENCE'
              AND o.owner = USER
            ORDER BY o.object_name";

        return await GetDatabaseObjectsFromQueryAsync(connection, sql, DatabaseObjectType.Sequence);
    }

    /// <summary>
    /// 取得觸發器清單
    /// </summary>
    private async Task<IEnumerable<DatabaseObject>> GetTriggersAsync(IDbConnection connection)
    {
        const string sql = @"
            SELECT
                o.object_name as Name,
                o.owner as Owner,
                o.created as CreatedDate,
                o.last_ddl_time as ModifiedDate,
                o.status as Status
            FROM all_objects o
            WHERE o.object_type = 'TRIGGER'
              AND o.owner = USER
            ORDER BY o.object_name";

        return await GetDatabaseObjectsFromQueryAsync(connection, sql, DatabaseObjectType.Trigger);
    }

    /// <summary>
    /// 取得索引清單
    /// </summary>
    private async Task<IEnumerable<DatabaseObject>> GetIndexesAsync(IDbConnection connection)
    {
        const string sql = @"
            SELECT
                o.object_name as Name,
                o.owner as Owner,
                o.created as CreatedDate,
                o.last_ddl_time as ModifiedDate,
                o.status as Status
            FROM all_objects o
            WHERE o.object_type = 'INDEX'
              AND o.owner = USER
            ORDER BY o.object_name";

        return await GetDatabaseObjectsFromQueryAsync(connection, sql, DatabaseObjectType.Index);
    }

    /// <summary>
    /// 從查詢結果建立資料庫物件清單
    /// </summary>
    private async Task<IEnumerable<DatabaseObject>> GetDatabaseObjectsFromQueryAsync(
        IDbConnection connection, string sql, DatabaseObjectType type)
    {
        var dataTable = await ExecuteQueryAsync(connection, sql);
        var objects = new List<DatabaseObject>();

        foreach (DataRow row in dataTable.Rows)
        {
            objects.Add(new DatabaseObject
            {
                Name = row["Name"]?.ToString() ?? string.Empty,
                Owner = row["Owner"]?.ToString() ?? string.Empty,
                Type = type,
                CreatedDate = row["CreatedDate"] is DateTime created ? created : DateTime.MinValue,
                ModifiedDate = row["ModifiedDate"] is DateTime modified ? modified : DateTime.MinValue,
                Status = row["Status"]?.ToString() ?? string.Empty
            });
        }

        return objects;
    }

    /// <summary>
    /// 取得資料表擁有者
    /// </summary>
    private async Task<string> GetTableOwnerAsync(IDbConnection connection, string tableName)
    {
        const string sql = "SELECT owner FROM all_tables WHERE table_name = :tableName AND owner = USER";
        
        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("tableName", tableName.ToUpper()));
        }

        var result = await Task.Run(() => command.ExecuteScalar());
        return result?.ToString() ?? string.Empty;
    }

    /// <summary>
    /// 取得資料表欄位資訊
    /// </summary>
    private async Task<IEnumerable<ColumnInfo>> GetColumnsAsync(IDbConnection connection, string tableName)
    {
        const string sql = @"
            SELECT 
                c.column_name,
                c.data_type,
                c.data_length,
                c.data_precision,
                c.data_scale,
                c.nullable,
                c.data_default,
                CASE WHEN pk.column_name IS NOT NULL THEN 'Y' ELSE 'N' END as is_primary_key
            FROM all_tab_columns c
            LEFT JOIN (
                SELECT cc.column_name, cc.table_name
                FROM all_constraints cons
                JOIN all_cons_columns cc ON cons.constraint_name = cc.constraint_name
                WHERE cons.constraint_type = 'P'
                AND cons.owner = USER
                AND cc.table_name = :tableName
            ) pk ON c.column_name = pk.column_name AND c.table_name = pk.table_name
            WHERE c.table_name = :tableName
            AND c.owner = USER
            ORDER BY c.column_id";

        using var command = connection.CreateCommand();
        command.CommandText = sql;

        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("tableName", tableName.ToUpper()));
        }

        var dataTable = new DataTable();
        using var adapter = new OracleDataAdapter((OracleCommand)command);
        await Task.Run(() => adapter.Fill(dataTable));
        var columns = new List<ColumnInfo>();

        foreach (DataRow row in dataTable.Rows)
        {
            columns.Add(new ColumnInfo
            {
                ColumnName = row["column_name"]?.ToString() ?? string.Empty,
                DataType = row["data_type"]?.ToString() ?? string.Empty,
                MaxLength = row["data_length"] is DBNull ? null : Convert.ToInt32(row["data_length"]),
                Precision = row["data_precision"] is DBNull ? null : Convert.ToInt32(row["data_precision"]),
                Scale = row["data_scale"] is DBNull ? null : Convert.ToInt32(row["data_scale"]),
                IsNullable = row["nullable"]?.ToString() == "Y",
                DefaultValue = row["data_default"]?.ToString()?.Trim() ?? string.Empty,
                IsPrimaryKey = row["is_primary_key"]?.ToString() == "Y"
            });
        }

        return columns;
    }

    /// <summary>
    /// 取得資料表索引資訊
    /// </summary>
    private async Task<IEnumerable<IndexInfo>> GetIndexesAsync(IDbConnection connection, string tableName)
    {
        const string sql = @"
            SELECT 
                i.index_name,
                i.index_type,
                i.uniqueness,
                LISTAGG(ic.column_name, ', ') WITHIN GROUP (ORDER BY ic.column_position) as columns
            FROM all_indexes i
            JOIN all_ind_columns ic ON i.index_name = ic.index_name AND i.owner = ic.index_owner
            WHERE i.table_name = :tableName
            AND i.owner = USER
            AND i.index_type != 'LOB'
            GROUP BY i.index_name, i.index_type, i.uniqueness
            ORDER BY i.index_name";

        using var command = connection.CreateCommand();
        command.CommandText = sql;

        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("tableName", tableName.ToUpper()));
        }

        var dataTable = new DataTable();
        using var adapter = new OracleDataAdapter((OracleCommand)command);
        await Task.Run(() => adapter.Fill(dataTable));
        var indexes = new List<IndexInfo>();

        foreach (DataRow row in dataTable.Rows)
        {
            var columnNames = row["columns"]?.ToString()?.Split(", ") ?? Array.Empty<string>();
            
            indexes.Add(new IndexInfo
            {
                IndexName = row["index_name"]?.ToString() ?? string.Empty,
                IndexType = row["index_type"]?.ToString() ?? string.Empty,
                IsUnique = row["uniqueness"]?.ToString() == "UNIQUE",
                Columns = columnNames.ToList()
            });
        }

        return indexes;
    }

    /// <summary>
    /// 取得資料表約束資訊
    /// </summary>
    private async Task<IEnumerable<ConstraintInfo>> GetConstraintsAsync(IDbConnection connection, string tableName)
    {
        const string sql = @"
            SELECT 
                c.constraint_name,
                c.constraint_type,
                LISTAGG(cc.column_name, ', ') WITHIN GROUP (ORDER BY cc.position) as columns,
                c.r_constraint_name,
                rc.table_name as referenced_table,
                LISTAGG(rcc.column_name, ', ') WITHIN GROUP (ORDER BY rcc.position) as referenced_columns
            FROM all_constraints c
            JOIN all_cons_columns cc ON c.constraint_name = cc.constraint_name AND c.owner = cc.owner
            LEFT JOIN all_constraints rc ON c.r_constraint_name = rc.constraint_name
            LEFT JOIN all_cons_columns rcc ON rc.constraint_name = rcc.constraint_name AND rc.owner = rcc.owner
            WHERE c.table_name = :tableName
            AND c.owner = USER
            GROUP BY c.constraint_name, c.constraint_type, c.r_constraint_name, rc.table_name
            ORDER BY c.constraint_name";

        using var command = connection.CreateCommand();
        command.CommandText = sql;

        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("tableName", tableName.ToUpper()));
        }

        var dataTable = new DataTable();
        using var adapter = new OracleDataAdapter((OracleCommand)command);
        await Task.Run(() => adapter.Fill(dataTable));
        var constraints = new List<ConstraintInfo>();

        foreach (DataRow row in dataTable.Rows)
        {
            constraints.Add(new ConstraintInfo
            {
                ConstraintName = row["constraint_name"]?.ToString() ?? string.Empty,
                ConstraintType = GetConstraintTypeDescription(row["constraint_type"]?.ToString()),
                Columns = row["columns"]?.ToString() ?? string.Empty,
                ReferencedTable = row["referenced_table"]?.ToString() ?? string.Empty,
                ReferencedColumns = row["referenced_columns"]?.ToString() ?? string.Empty
            });
        }

        return constraints;
    }

    /// <summary>
    /// 取得資料表觸發器資訊
    /// </summary>
    private async Task<IEnumerable<TriggerInfo>> GetTriggersAsync(IDbConnection connection, string tableName)
    {
        const string sql = @"
            SELECT 
                trigger_name,
                trigger_type,
                triggering_event,
                status
            FROM all_triggers
            WHERE table_name = :tableName
            AND owner = USER
            ORDER BY trigger_name";

        using var command = connection.CreateCommand();
        command.CommandText = sql;

        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("tableName", tableName.ToUpper()));
        }

        var dataTable = new DataTable();
        using var adapter = new OracleDataAdapter((OracleCommand)command);
        await Task.Run(() => adapter.Fill(dataTable));
        var triggers = new List<TriggerInfo>();

        foreach (DataRow row in dataTable.Rows)
        {
            triggers.Add(new TriggerInfo
            {
                TriggerName = row["trigger_name"]?.ToString() ?? string.Empty,
                TriggerType = row["trigger_type"]?.ToString() ?? string.Empty,
                Event = row["triggering_event"]?.ToString() ?? string.Empty,
                Status = row["status"]?.ToString() ?? string.Empty
            });
        }

        return triggers;
    }

    /// <summary>
    /// 取得約束類型描述
    /// </summary>
    private static string GetConstraintTypeDescription(string? constraintType)
    {
        return constraintType switch
        {
            "P" => "Primary Key",
            "R" => "Foreign Key",
            "U" => "Unique",
            "C" => "Check",
            "V" => "View Check",
            "O" => "View Read Only",
            _ => constraintType ?? string.Empty
        };
    }
}