using System;
using System.Data;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 索引編輯器 ViewModel
    /// </summary>
    public class IndexEditorViewModel : BaseObjectEditorViewModel
    {
        private IndexDefinition _indexDefinition;
        private string _ddlPreview = string.Empty;
        private bool _isOperationInProgress;

        /// <summary>
        /// 索引定義
        /// </summary>
        public IndexDefinition IndexDefinition
        {
            get => _indexDefinition;
            private set => SetProperty(ref _indexDefinition, value);
        }

        /// <summary>
        /// DDL 預覽
        /// </summary>
        public string DdlPreview
        {
            get => _ddlPreview;
            private set => SetProperty(ref _ddlPreview, value);
        }

        /// <summary>
        /// 是否有操作正在進行中
        /// </summary>
        public bool IsOperationInProgress
        {
            get => _isOperationInProgress;
            private set => SetProperty(ref _isOperationInProgress, value);
        }

        /// <summary>
        /// 重建索引命令
        /// </summary>
        public ICommand RebuildIndexCommand { get; }

        /// <summary>
        /// 分析索引命令
        /// </summary>
        public ICommand AnalyzeIndexCommand { get; }

        /// <summary>
        /// 複製 DDL 命令
        /// </summary>
        public ICommand CopyDdlCommand { get; }

        /// <summary>
        /// 儲存 DDL 命令
        /// </summary>
        public ICommand SaveDdlCommand { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="indexName">索引名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        public IndexEditorViewModel(
            string indexName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(indexName, DatabaseObjectType.Index, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            _indexDefinition = new IndexDefinition { Name = indexName };

            // 初始化命令
            RebuildIndexCommand = new AsyncRelayCommand(RebuildIndexAsync, CanRebuildIndex);
            AnalyzeIndexCommand = new AsyncRelayCommand(AnalyzeIndexAsync, CanAnalyzeIndex);
            CopyDdlCommand = new RelayCommand(CopyDdlToClipboard);
            SaveDdlCommand = new RelayCommand(SaveDdlToFile);
        }

        /// <summary>
        /// 載入索引定義
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            _isInitializing = true;
            try
            {
                // 載入索引定義
                var definition = await _objectEditorService.GetIndexDefinitionAsync(connection, ObjectName);
                IndexDefinition = definition;

                // 更新 DDL 預覽
                UpdateDdlPreview();
            }
            finally
            {
                _isInitializing = false;
            }
        }

        /// <summary>
        /// 儲存索引定義 (索引不支援直接儲存，僅支援重建)
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            // 索引不支援直接儲存，僅支援重建
            await Task.CompletedTask;
        }

        /// <summary>
        /// 產生 DDL 腳本
        /// </summary>
        /// <returns>DDL 腳本</returns>
        protected override string GenerateScript()
        {
            return GenerateIndexScript();
        }

        /// <summary>
        /// 驗證索引定義
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            return IndexDefinition?.Validate() ?? new ValidationResult();
        }

        /// <summary>
        /// 重建索引
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task RebuildIndexAsync()
        {
            if (IsOperationInProgress || IsLoading)
                return;

            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            try
            {
                IsOperationInProgress = true;
                StatusMessage = $"正在重建索引 {ObjectName}...";
                HasError = false;
                ErrorMessage = string.Empty;

                // 重建索引
                await _objectEditorService.RebuildIndexAsync(connection, ObjectName);

                // 重新載入索引定義
                await LoadObjectAsync();

                StatusMessage = $"索引 {ObjectName} 已重建完成";
            }
            catch (Exception ex)
            {
                HandleError(ex, "重建索引");
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        /// <summary>
        /// 分析索引
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task AnalyzeIndexAsync()
        {
            if (IsOperationInProgress || IsLoading)
                return;

            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            try
            {
                IsOperationInProgress = true;
                StatusMessage = $"正在分析索引 {ObjectName}...";
                HasError = false;
                ErrorMessage = string.Empty;

                // 執行分析索引的 SQL
                var sql = $"ANALYZE INDEX {IndexDefinition.Owner}.{ObjectName} COMPUTE STATISTICS";
                await _databaseService.ExecuteNonQueryAsync(connection, sql);

                // 重新載入索引定義
                await LoadObjectAsync();

                StatusMessage = $"索引 {ObjectName} 已分析完成";
            }
            catch (Exception ex)
            {
                HandleError(ex, "分析索引");
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        /// <summary>
        /// 複製 DDL 到剪貼簿
        /// </summary>
        private void CopyDdlToClipboard()
        {
            try
            {
                System.Windows.Clipboard.SetText(DdlPreview);
                StatusMessage = "DDL 已複製到剪貼簿";
            }
            catch (Exception ex)
            {
                HandleError(ex, "複製 DDL");
            }
        }

        /// <summary>
        /// 儲存 DDL 到檔案
        /// </summary>
        private void SaveDdlToFile()
        {
            try
            {
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    FileName = $"{ObjectName}_DDL.sql",
                    DefaultExt = ".sql",
                    Filter = "SQL 檔案 (*.sql)|*.sql|所有檔案 (*.*)|*.*"
                };

                if (dialog.ShowDialog() == true)
                {
                    System.IO.File.WriteAllText(dialog.FileName, DdlPreview);
                    StatusMessage = $"DDL 已儲存至 {dialog.FileName}";
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "儲存 DDL");
            }
        }

        /// <summary>
        /// 更新 DDL 預覽
        /// </summary>
        private void UpdateDdlPreview()
        {
            DdlPreview = GenerateIndexScript();
        }

        /// <summary>
        /// 產生索引 DDL 腳本
        /// </summary>
        /// <returns>索引 DDL 腳本</returns>
        private string GenerateIndexScript()
        {
            if (IndexDefinition == null)
                return string.Empty;

            var script = new System.Text.StringBuilder();

            // 產生 CREATE INDEX 語句
            script.AppendLine($"-- 索引定義: {IndexDefinition.Owner}.{IndexDefinition.Name}");
            script.AppendLine($"-- 表格: {IndexDefinition.TableName}");
            script.AppendLine($"-- 類型: {IndexDefinition.Type}");
            script.AppendLine($"-- 狀態: {IndexDefinition.Status}");
            script.AppendLine($"-- 最後分析時間: {IndexDefinition.LastAnalyzed:yyyy-MM-dd HH:mm:ss}");
            script.AppendLine();

            script.Append($"CREATE ");
            if (IndexDefinition.IsUnique)
                script.Append("UNIQUE ");

            script.Append($"INDEX {IndexDefinition.Owner}.{IndexDefinition.Name} ON {IndexDefinition.Owner}.{IndexDefinition.TableName} (");

            // 加入索引欄位
            var columnList = string.Empty;
            foreach (var column in IndexDefinition.Columns)
            {
                if (!string.IsNullOrEmpty(columnList))
                    columnList += ", ";

                columnList += column.ColumnName;
                if (column.IsDescending)
                    columnList += " DESC";
            }
            script.Append(columnList);
            script.AppendLine(")");

            // 加入表空間
            if (!string.IsNullOrEmpty(IndexDefinition.Tablespace))
                script.AppendLine($"TABLESPACE {IndexDefinition.Tablespace}");

            script.AppendLine(";");
            script.AppendLine();

            // 加入重建索引的語句
            script.AppendLine($"-- 重建索引");
            script.AppendLine($"ALTER INDEX {IndexDefinition.Owner}.{IndexDefinition.Name} REBUILD;");
            script.AppendLine();

            // 加入分析索引的語句
            script.AppendLine($"-- 分析索引");
            script.AppendLine($"ANALYZE INDEX {IndexDefinition.Owner}.{IndexDefinition.Name} COMPUTE STATISTICS;");

            return script.ToString();
        }

        /// <summary>
        /// 是否可以重建索引
        /// </summary>
        /// <returns>是否可以重建索引</returns>
        private bool CanRebuildIndex()
        {
            return !IsLoading && !IsOperationInProgress && _getConnection() != null;
        }

        /// <summary>
        /// 是否可以分析索引
        /// </summary>
        /// <returns>是否可以分析索引</returns>
        private bool CanAnalyzeIndex()
        {
            return !IsLoading && !IsOperationInProgress && _getConnection() != null;
        }
    }
}