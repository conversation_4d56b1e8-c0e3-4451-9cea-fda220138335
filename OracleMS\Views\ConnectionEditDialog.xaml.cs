using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;
using System;
using System.ComponentModel;
using System.Data;
using System.Windows;
using System.Windows.Controls;

namespace OracleMS.Views;

public partial class ConnectionEditDialog : Window, INotifyPropertyChanged
{
    public ConnectionInfo ConnectionInfo { get; private set; }
    public IDbConnection? DatabaseConnection { get; private set; }
    
    private bool _isTestSuccessful;
    public bool IsTestSuccessful 
    { 
        get => _isTestSuccessful;
        private set
        {
            _isTestSuccessful = value;
            OnPropertyChanged(nameof(IsTestSuccessful));
            OnPropertyChanged(nameof(IsTestFailed));
        }
    }
    
    public bool IsTestFailed => !IsTestSuccessful && !string.IsNullOrEmpty(TestResult);
    
    private bool _isTesting;
    public bool IsTesting
    {
        get => _isTesting;
        private set
        {
            _isTesting = value;
            OnPropertyChanged(nameof(IsTesting));
        }
    }
    
    private string _testResult = string.Empty;
    public string TestResult
    {
        get => _testResult;
        private set
        {
            _testResult = value;
            OnPropertyChanged(nameof(TestResult));
            OnPropertyChanged(nameof(IsTestFailed));
        }
    }
    
    private readonly ConnectionManagerViewModel _viewModel;

    public event PropertyChangedEventHandler? PropertyChanged;

    public ConnectionEditDialog(ConnectionInfo connectionInfo, ConnectionManagerViewModel viewModel)
    {
        InitializeComponent();
        
        ConnectionInfo = connectionInfo ?? new ConnectionInfo();
        _viewModel = viewModel;
        
        DataContext = ConnectionInfo;
        
        // Set password if available
        if (!string.IsNullOrEmpty(ConnectionInfo.Password))
        {
            PasswordBox.Password = ConnectionInfo.Password;
        }
        
        // Focus on connection name
        Loaded += (s, e) => 
        {
            if (string.IsNullOrEmpty(ConnectionInfo.Name))
            {
                var nameTextBox = FindName("ConnectionNameTextBox") as TextBox;
                nameTextBox?.Focus();
            }
        };
    }

    private void OnPasswordChanged(object sender, RoutedEventArgs e)
    {
        if (sender is PasswordBox passwordBox)
        {
            ConnectionInfo.Password = passwordBox.Password;
        }
    }

    private async void OnTestConnection(object sender, RoutedEventArgs e)
    {
        try
        {
            IsTesting = true;
            TestResult = "測試連線中...";
            
            // Update the editing connection in the view model
            _viewModel.EditingConnection = ConnectionInfo;
            
            // Execute test command
            if (_viewModel.TestConnectionCommand.CanExecute(null))
            {
                await ((AsyncRelayCommand)_viewModel.TestConnectionCommand).ExecuteAsync(null);
            }
            
            // Update test result display
            TestResult = _viewModel.TestResult;
            IsTestSuccessful = _viewModel.TestResult.Contains("成功");
        }
        catch (Exception ex)
        {
            TestResult = $"測試連線時發生錯誤: {ex.Message}";
            IsTestSuccessful = false;
        }
        finally
        {
            IsTesting = false;
        }
    }

    private async void OnOK(object sender, RoutedEventArgs e)
    {
        // Validate connection info
        var (isValid, errorMessage) = ConnectionInfo.Validate();
        if (!isValid)
        {
            MessageBox.Show(errorMessage, "驗證錯誤",
                          MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            // 更新 ViewModel 中的連線資訊
            _viewModel.EditingConnection = ConnectionInfo;

            // 透過 ConnectionService 創建連線
            var connectionService = App.ServiceProvider?.GetService<IConnectionService>();
            if (connectionService == null)
            {
                MessageBox.Show("無法取得連線服務", "系統錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 先測試連線
            //if (_viewModel.TestConnectionCommand.CanExecute(null))
            //{
            //    await ((AsyncRelayCommand)_viewModel.TestConnectionCommand).ExecuteAsync(null);

            //    if (!_viewModel.TestResult.Contains("成功"))
            //    {
            //        MessageBox.Show($"連線測試失敗: {_viewModel.TestResult}", "連線錯誤",
            //                      MessageBoxButton.OK, MessageBoxImage.Error);
            //        return;
            //    }
            //}

            // 創建實際的連線
            var connectionProvider = App.ServiceProvider?.GetService<IConnectionProvider>();
            if (connectionProvider == null)
            {
                MessageBox.Show("無法取得連線提供者", "系統錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            DatabaseConnection = await connectionProvider.CreateConnectionAsync(ConnectionInfo);

            // 測試連線是否正常
            if (DatabaseConnection.State != ConnectionState.Open)
            {
                DatabaseConnection.Open();
            }

            // 確認連線成功後才設定 DialogResult
            if (DatabaseConnection != null && DatabaseConnection.State == ConnectionState.Open)
            {
                // 儲存連線資訊到設定檔
                try
                {
                    // 儲存連線資訊，ConnectionService.SaveConnectionAsync 會檢查是否已存在
                    // 如果已存在，會更新而不是重複儲存
                    await connectionService.SaveConnectionAsync(ConnectionInfo);
                }
                catch (Exception saveEx)
                {
                    // 儲存失敗不影響連線使用，只顯示警告
                    MessageBox.Show($"連線成功，但儲存連線資訊失敗: {saveEx.Message}", "警告",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("連線建立失敗", "連線錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                DatabaseConnection?.Dispose();
                DatabaseConnection = null;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"連線失敗: {ex.Message}", "連線錯誤",
                          MessageBoxButton.OK, MessageBoxImage.Error);
            DatabaseConnection?.Dispose();
            DatabaseConnection = null;
        }
    }

    private void OnCancel(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
    
    private void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}