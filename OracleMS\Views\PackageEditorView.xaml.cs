using System.Windows;
using System.Windows.Controls;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System.Xml;
using System.Reflection;
using System;
using System.IO;
using System.ComponentModel;
using OracleMS.ViewModels;

namespace OracleMS.Views
{
    /// <summary>
    /// PackageEditorView.xaml 的互動邏輯
    /// </summary>
    public partial class PackageEditorView : UserControl
    {
        public PackageEditorView()
        {
            InitializeComponent();

            // 載入 PL/SQL 語法高亮定義
            LoadSyntaxHighlighting();

            // 設定 DataContext 變更事件
            DataContextChanged += PackageEditorView_DataContextChanged;
        }

        private void PackageEditorView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.OldValue is PackageEditorViewModel oldViewModel)
            {
                // 解除舊的 ViewModel 事件綁定
                oldViewModel.PropertyChanged -= ViewModel_PropertyChanged;
                SpecificationEditor.TextChanged -= SpecificationEditor_TextChanged;
                BodyEditor.TextChanged -= BodyEditor_TextChanged;
            }

            if (e.NewValue is PackageEditorViewModel newViewModel)
            {
                // 綁定新的 ViewModel 事件
                newViewModel.PropertyChanged += ViewModel_PropertyChanged;
                SpecificationEditor.TextChanged += SpecificationEditor_TextChanged;
                BodyEditor.TextChanged += BodyEditor_TextChanged;

                // 只有在 ViewModel 已經有內容時才初始化編輯器文字
                if (!string.IsNullOrEmpty(newViewModel.SpecificationSource))
                {
                    System.Diagnostics.Debug.WriteLine($"PackageEditor DataContext 變更，初始化規格編輯器文字: 長度={newViewModel.SpecificationSource.Length}");
                    SpecificationEditor.Text = newViewModel.SpecificationSource;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("PackageEditor DataContext 變更，但 SpecificationSource 為空，等待 PropertyChanged");
                }

                if (!string.IsNullOrEmpty(newViewModel.BodySource))
                {
                    System.Diagnostics.Debug.WriteLine($"PackageEditor DataContext 變更，初始化主體編輯器文字: 長度={newViewModel.BodySource.Length}");
                    BodyEditor.Text = newViewModel.BodySource;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("PackageEditor DataContext 變更，但 BodySource 為空，等待 PropertyChanged");
                }
            }
        }

        private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            var viewModel = sender as PackageEditorViewModel;
            if (viewModel == null) return;

            if (e.PropertyName == nameof(PackageEditorViewModel.SpecificationSource))
            {
                // 如果 ViewModel 的 SpecificationSource 屬性變更，更新編輯器文字
                if (SpecificationEditor.Text != viewModel.SpecificationSource)
                {
                    System.Diagnostics.Debug.WriteLine($"PackageEditor 更新規格編輯器文字: 長度={viewModel.SpecificationSource?.Length ?? 0}");
                    SpecificationEditor.Text = viewModel.SpecificationSource ?? string.Empty;
                }
            }
            else if (e.PropertyName == nameof(PackageEditorViewModel.BodySource))
            {
                // 如果 ViewModel 的 BodySource 屬性變更，更新編輯器文字
                if (BodyEditor.Text != viewModel.BodySource)
                {
                    System.Diagnostics.Debug.WriteLine($"PackageEditor 更新主體編輯器文字: 長度={viewModel.BodySource?.Length ?? 0}");
                    BodyEditor.Text = viewModel.BodySource ?? string.Empty;
                }
            }
        }

        private void SpecificationEditor_TextChanged(object? sender, EventArgs e)
        {
            // 將編輯器文字變更同步到 ViewModel
            var viewModel = DataContext as PackageEditorViewModel;
            if (viewModel != null && SpecificationEditor.Text != viewModel.SpecificationSource)
            {
                viewModel.SpecificationSource = SpecificationEditor.Text;
            }
        }

        private void BodyEditor_TextChanged(object? sender, EventArgs e)
        {
            // 將編輯器文字變更同步到 ViewModel
            var viewModel = DataContext as PackageEditorViewModel;
            if (viewModel != null && BodyEditor.Text != viewModel.BodySource)
            {
                viewModel.BodySource = BodyEditor.Text;
            }
        }

        private void LoadSyntaxHighlighting()
        {
            try
            {
                // 嘗試載入內嵌的 PL/SQL 語法高亮定義
                using var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("OracleMS.Resources.PLSQL.xshd");
                if (stream != null)
                {
                    using var reader = new XmlTextReader(stream);
                    var highlightingDefinition = HighlightingLoader.Load(reader, HighlightingManager.Instance);

                    // 設定編輯器使用 PL/SQL 語法高亮
                    SpecificationEditor.SyntaxHighlighting = highlightingDefinition;
                    BodyEditor.SyntaxHighlighting = highlightingDefinition;
                }
                else
                {
                    // 如果找不到內嵌資源，嘗試從檔案載入
                    var xshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "PLSQL.xshd");
                    if (File.Exists(xshdFile))
                    {
                        using var reader = new XmlTextReader(xshdFile);
                        var highlightingDefinition = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        SpecificationEditor.SyntaxHighlighting = highlightingDefinition;
                        BodyEditor.SyntaxHighlighting = highlightingDefinition;
                    }
                    else
                    {
                        // 如果找不到 PL/SQL 定義，使用 SQL 定義作為替代
                        var sqlXshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "SQL.xshd");
                        if (File.Exists(sqlXshdFile))
                        {
                            using var reader = new XmlTextReader(sqlXshdFile);
                            var highlightingDefinition = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                            SpecificationEditor.SyntaxHighlighting = highlightingDefinition;
                            BodyEditor.SyntaxHighlighting = highlightingDefinition;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 載入語法高亮定義失敗，記錄錯誤但不中斷程式
                System.Diagnostics.Debug.WriteLine($"載入 PL/SQL 語法高亮定義失敗: {ex.Message}");
            }
        }
    }
}