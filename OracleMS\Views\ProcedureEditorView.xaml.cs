using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System.Xml;
using System.Reflection;
using System.IO;
using OracleMS.ViewModels;

namespace OracleMS.Views
{
    /// <summary>
    /// ProcedureEditorView.xaml 的互動邏輯
    /// </summary>
    public partial class ProcedureEditorView : UserControl
    {
        private ProcedureEditorViewModel? ViewModel => DataContext as ProcedureEditorViewModel;

        public ProcedureEditorView()
        {
            InitializeComponent();

            // 載入 PL/SQL 語法高亮定義
            LoadPlSqlSyntaxHighlighting();

            // 設定 TextEditor 的文字變更事件（在建構函式中綁定，避免重複執行）
            SourceCodeEditor.TextChanged += SourceCodeEditor_TextChanged;

            // 設定 DataContext 變更事件
            DataContextChanged += ProcedureEditorView_DataContextChanged;

            // 設定快捷鍵
            SetupKeyBindings();
        }

        private void ProcedureEditorView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.OldValue is ProcedureEditorViewModel oldViewModel)
            {
                // 解除舊的 ViewModel 事件綁定
                oldViewModel.PropertyChanged -= ViewModel_PropertyChanged;
            }

            if (e.NewValue is ProcedureEditorViewModel newViewModel)
            {
                // 綁定新的 ViewModel 事件
                newViewModel.PropertyChanged += ViewModel_PropertyChanged;

                // 初始化編輯器文字（仿照 ViewEditor 的做法）
                SourceCodeEditor.Text = newViewModel.ProcedureSource ?? string.Empty;
                System.Diagnostics.Debug.WriteLine($"ProcedureEditor DataContext 變更，初始化編輯器文字: 長度={newViewModel.ProcedureSource?.Length ?? 0}");
            }
        }

        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ProcedureEditorViewModel.ProcedureSource))
            {
                // 如果 ViewModel 的 ProcedureSource 屬性變更，更新編輯器文字
                var viewModel = sender as ProcedureEditorViewModel;
                if (viewModel != null && SourceCodeEditor.Text != viewModel.ProcedureSource)
                {
                    System.Diagnostics.Debug.WriteLine($"更新編輯器文字: 長度={viewModel.ProcedureSource?.Length ?? 0}");
                    SourceCodeEditor.Text = viewModel.ProcedureSource ?? string.Empty;
                }
            }
        }

        private void SourceCodeEditor_TextChanged(object? sender, EventArgs e)
        {
            // 將編輯器文字變更同步到 ViewModel
            if (ViewModel != null && SourceCodeEditor.Text != ViewModel.ProcedureSource)
            {
                ViewModel.ProcedureSource = SourceCodeEditor.Text;
            }
        }

        private void LoadPlSqlSyntaxHighlighting()
        {
            try
            {
                // 嘗試載入內嵌的 PL/SQL 語法高亮定義
                using var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("OracleMS.Resources.PLSQL.xshd");
                if (stream != null)
                {
                    using var reader = new XmlTextReader(stream);
                    SourceCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                }
                else
                {
                    // 如果找不到內嵌資源，嘗試從檔案載入
                    var xshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "PLSQL.xshd");
                    if (File.Exists(xshdFile))
                    {
                        using var reader = new XmlTextReader(xshdFile);
                        SourceCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                    }
                    else
                    {
                        // 如果找不到 PL/SQL 定義，使用 SQL 定義作為替代
                        var sqlXshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "SQL.xshd");
                        if (File.Exists(sqlXshdFile))
                        {
                            using var reader = new XmlTextReader(sqlXshdFile);
                            SourceCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 載入語法高亮定義失敗，記錄錯誤但不中斷程式
                System.Diagnostics.Debug.WriteLine($"載入 PL/SQL 語法高亮定義失敗: {ex.Message}");
            }
        }

        private void SetupKeyBindings()
        {
            // 設定 Ctrl+S 快捷鍵
            var saveBinding = new KeyBinding
            {
                Key = Key.S,
                Modifiers = ModifierKeys.Control,
                Command = ApplicationCommands.Save
            };
            
            this.InputBindings.Add(saveBinding);
            
            // 設定 F5 快捷鍵編譯
            var compileBinding = new KeyBinding
            {
                Key = Key.F5,
                Command = new RelayCommand(() => ViewModel?.CompileCommand.Execute(null))
            };
            
            this.InputBindings.Add(compileBinding);
            
            // 設定 Ctrl+F5 快捷鍵驗證語法
            var validateBinding = new KeyBinding
            {
                Key = Key.F5,
                Modifiers = ModifierKeys.Control,
                Command = new RelayCommand(() => ViewModel?.ValidateCommand.Execute(null))
            };
            
            this.InputBindings.Add(validateBinding);
            
            // 設定命令綁定
            this.CommandBindings.Add(new CommandBinding(ApplicationCommands.Save, (s, e) => 
            {
                if (ViewModel != null && ViewModel.SaveCommand.CanExecute(null))
                {
                    ViewModel.SaveCommand.Execute(null);
                }
            }));
        }
    }


}