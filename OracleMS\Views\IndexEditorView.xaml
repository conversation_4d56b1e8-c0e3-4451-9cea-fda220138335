<UserControl x:Class="OracleMS.Views.IndexEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewmodels="clr-namespace:OracleMS.ViewModels"
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance Type=viewmodels:IndexEditorViewModel}">

    <UserControl.Resources>
        <!-- Converter for boolean to visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Style for toolbar buttons -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="MinWidth" Value="75"/>
            <Setter Property="Height" Value="28"/>
        </Style>

        <!-- Style for status bar text -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="*"/> <!-- Content Area -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
            <Button Command="{Binding RebuildIndexCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="重建索引">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" Margin="0,0,4,0"/>
                        <TextBlock Text="重建索引"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding AnalyzeIndexCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="分析索引">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📊" Margin="0,0,4,0"/>
                        <TextBlock Text="分析索引"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding RefreshCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="重新載入索引資訊">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" Margin="0,0,4,0"/>
                        <TextBlock Text="重新載入"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding GenerateScriptCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="產生 DDL 腳本">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📝" Margin="0,0,4,0"/>
                        <TextBlock Text="產生腳本"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <!-- Progress indicator -->
            <ProgressBar Width="100" Height="16" 
                         IsIndeterminate="True"
                         Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                         Margin="10,0"/>
        </ToolBar>

        <!-- Content Area -->
        <TabControl Grid.Row="1">
            <!-- Index Properties Tab -->
            <TabItem Header="索引屬性">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Index Properties -->
                    <GroupBox Grid.Column="0" Header="基本資訊" Margin="0,0,5,0">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <StackPanel Margin="5">
                                <TextBlock Text="索引名稱:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.Name, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="擁有者:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.Owner, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="索引類型:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.IndexType, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="表格名稱:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.TableName, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="唯一性:" Margin="0,5,0,2"/>
                                <CheckBox IsChecked="{Binding IndexDefinition.IsUnique, Mode=OneWay}" IsEnabled="False" Margin="0,0,0,10"/>

                                <TextBlock Text="索引欄位:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.ColumnList, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10" TextWrapping="Wrap"/>

                                <TextBlock Text="索引狀態:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.Status, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>
                            </StackPanel>
                        </ScrollViewer>
                    </GroupBox>

                    <!-- Index Statistics -->
                    <GroupBox Grid.Column="1" Header="索引統計資訊" Margin="5,0,0,0">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <StackPanel Margin="5">
                                <TextBlock Text="區塊數量:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.NumBlocks, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="不同鍵值數量:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.DistinctKeys, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="平均葉節點數量:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.AvgLeafBlocks, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="平均資料區塊數量:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.AvgDataBlocks, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="叢集因子:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.ClusteringFactor, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                <TextBlock Text="最後分析時間:" Margin="0,5,0,2"/>
                                <TextBox Text="{Binding IndexDefinition.LastAnalyzed, Mode=OneWay, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}" IsReadOnly="True" Margin="0,0,0,10"/>
                            </StackPanel>
                        </ScrollViewer>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- DDL Preview Tab -->
            <TabItem Header="DDL 預覽">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- DDL Toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <Button Command="{Binding CopyDdlCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="複製 DDL 腳本">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📋" Margin="0,0,4,0"/>
                                    <TextBlock Text="複製腳本"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding SaveDdlCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="儲存 DDL 腳本至檔案">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="💾" Margin="0,0,4,0"/>
                                    <TextBlock Text="儲存腳本"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- DDL Preview Editor -->
                    <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1">
                        <avalonedit:TextEditor x:Name="DdlPreviewEditor"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               ShowLineNumbers="True"
                                               WordWrap="False"
                                               IsReadOnly="True"
                                               HorizontalScrollBarVisibility="Auto"
                                               VerticalScrollBarVisibility="Auto"
                                               Background="White"
                                               Foreground="Black">
                            <avalonedit:TextEditor.Options>
                                <avalonedit:TextEditorOptions ShowSpaces="False"
                                                              ShowTabs="False"
                                                              ShowEndOfLine="False"
                                                              ShowBoxForControlCharacters="False"
                                                              ConvertTabsToSpaces="True"
                                                              IndentationSize="4"/>
                            </avalonedit:TextEditor.Options>
                        </avalonedit:TextEditor>
                    </Border>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="{Binding ObjectName}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding IndexDefinition.TableName, StringFormat='表格: {0}'}" 
                           Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                            <Setter Property="Text" Value="就緒"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Text" Value="載入中..."/>
                                    <Setter Property="Foreground" Value="Blue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsOperationInProgress}" Value="True">
                                    <Setter Property="Text" Value="操作進行中..."/>
                                    <Setter Property="Foreground" Value="Orange"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>