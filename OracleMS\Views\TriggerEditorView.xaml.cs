using System.ComponentModel;
using System.Windows.Controls;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System;
using System.IO;
using System.Reflection;
using System.Windows;
using System.Xml;
using OracleMS.ViewModels;

namespace OracleMS.Views
{
    /// <summary>
    /// TriggerEditorView.xaml 的互動邏輯
    /// </summary>
    public partial class TriggerEditorView : UserControl
    {
        public TriggerEditorView()
        {
            InitializeComponent();
            
            // 載入 PL/SQL 語法高亮定義
            LoadSyntaxHighlighting();
            
            // 訂閱 Loaded 事件
            Loaded += TriggerEditorView_Loaded;

            // 設定 DataContext 變更時的處理
            DataContextChanged += OnDataContextChanged;
        }

        private void TriggerEditorView_Loaded(object sender, RoutedEventArgs e)
        {
            // 設定 AvalonEdit 的初始焦點
            if (TriggerCodeEditor != null)
            {
                TriggerCodeEditor.Focus();
            }
        }

        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // 取消訂閱舊的 ViewModel
            if (e.OldValue is TriggerEditorViewModel oldViewModel)
            {
                oldViewModel.PropertyChanged -= OnViewModelPropertyChanged;
            }

            // 訂閱新的 ViewModel
            if (e.NewValue is TriggerEditorViewModel newViewModel)
            {
                newViewModel.PropertyChanged += OnViewModelPropertyChanged;

                // 初始設定觸發器源碼
                if (TriggerCodeEditor != null)
                {
                    TriggerCodeEditor.Text = newViewModel.TriggerSource ?? string.Empty;
                }
            }
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(TriggerEditorViewModel.TriggerSource))
            {
                if (sender is TriggerEditorViewModel viewModel && TriggerCodeEditor != null)
                {
                    TriggerCodeEditor.Text = viewModel.TriggerSource ?? string.Empty;
                }
            }
        }

        private void LoadSyntaxHighlighting()
        {
            try
            {
                // 嘗試載入內嵌的 PL/SQL 語法高亮定義
                using var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("OracleMS.Resources.PLSQL.xshd");
                if (stream != null)
                {
                    using var reader = new XmlTextReader(stream);
                    TriggerCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                }
                else
                {
                    // 如果找不到內嵌資源，嘗試從檔案載入
                    var xshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "PLSQL.xshd");
                    if (File.Exists(xshdFile))
                    {
                        using var reader = new XmlTextReader(xshdFile);
                        TriggerCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                    }
                    else
                    {
                        // 如果找不到 PL/SQL 定義，使用 SQL 定義作為替代
                        var sqlXshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "SQL.xshd");
                        if (File.Exists(sqlXshdFile))
                        {
                            using var reader = new XmlTextReader(sqlXshdFile);
                            TriggerCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 載入語法高亮定義失敗，記錄錯誤但不中斷程式
                System.Diagnostics.Debug.WriteLine($"載入 PL/SQL 語法高亮定義失敗: {ex.Message}");
            }
        }
    }
}