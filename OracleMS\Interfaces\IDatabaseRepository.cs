using System.Data;
using OracleMS.Models;

namespace OracleMS.Interfaces;

/// <summary>
/// 資料庫操作儲存庫介面
/// </summary>
public interface IDatabaseRepository
{
    /// <summary>
    /// 取得資料庫物件清單
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="type">物件類型</param>
    /// <returns>資料庫物件清單</returns>
    Task<IEnumerable<DatabaseObject>> GetDatabaseObjectsAsync(IDbConnection connection, DatabaseObjectType type);

    /// <summary>
    /// 執行查詢並返回 DataTable
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 查詢語句</param>
    /// <returns>查詢結果</returns>
    Task<DataTable> ExecuteQueryAsync(IDbConnection connection, string sql);

    /// <summary>
    /// 執行非查詢 SQL 語句
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="sql">SQL 語句</param>
    /// <returns>受影響的資料列數</returns>
    Task<int> ExecuteNonQueryAsync(IDbConnection connection, string sql);

    /// <summary>
    /// 取得資料表結構資訊
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="tableName">資料表名稱</param>
    /// <returns>資料表結構</returns>
    Task<TableSchema> GetTableSchemaAsync(IDbConnection connection, string tableName);

    /// <summary>
    /// 取得資料表清單
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <returns>資料表清單</returns>
    Task<IEnumerable<DatabaseObject>> GetTablesAsync(IDbConnection connection);

    /// <summary>
    /// 取得檢視清單
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <returns>檢視清單</returns>
    Task<IEnumerable<DatabaseObject>> GetViewsAsync(IDbConnection connection);

    /// <summary>
    /// 取得預存程序清單
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <returns>預存程序清單</returns>
    Task<IEnumerable<DatabaseObject>> GetProceduresAsync(IDbConnection connection);

    /// <summary>
    /// 取得函數清單
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <returns>函數清單</returns>
    Task<IEnumerable<DatabaseObject>> GetFunctionsAsync(IDbConnection connection);
}