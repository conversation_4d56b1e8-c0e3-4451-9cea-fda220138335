using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System.Xml;
using System.Reflection;
using System;
using System.IO;
using OracleMS.ViewModels;

namespace OracleMS.Views
{
    /// <summary>
    /// IndexEditorView.xaml 的互動邏輯
    /// </summary>
    public partial class IndexEditorView : UserControl
    {
        public IndexEditorView()
        {
            InitializeComponent();

            // 載入 SQL 語法高亮定義
            try
            {
                using (var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("OracleMS.Resources.SQL.xshd"))
                {
                    if (stream != null)
                    {
                        using (var reader = new XmlTextReader(stream))
                        {
                            DdlPreviewEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果無法載入語法高亮定義，則使用預設
                System.Diagnostics.Debug.WriteLine($"無法載入 SQL 語法高亮定義: {ex.Message}");
            }

            // 設定 DataContext 變更時的處理
            DataContextChanged += OnDataContextChanged;
        }

        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // 取消訂閱舊的 ViewModel
            if (e.OldValue is IndexEditorViewModel oldViewModel)
            {
                oldViewModel.PropertyChanged -= OnViewModelPropertyChanged;
            }

            // 訂閱新的 ViewModel
            if (e.NewValue is IndexEditorViewModel newViewModel)
            {
                newViewModel.PropertyChanged += OnViewModelPropertyChanged;

                // 初始設定 DDL 預覽文字
                if (DdlPreviewEditor != null)
                {
                    DdlPreviewEditor.Text = newViewModel.DdlPreview ?? string.Empty;
                }
            }
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(IndexEditorViewModel.DdlPreview))
            {
                if (sender is IndexEditorViewModel viewModel && DdlPreviewEditor != null)
                {
                    DdlPreviewEditor.Text = viewModel.DdlPreview ?? string.Empty;
                }
            }
        }
    }
}