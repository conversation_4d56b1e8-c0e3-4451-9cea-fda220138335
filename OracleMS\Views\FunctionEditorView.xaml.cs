using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System.Xml;
using System.Reflection;
using System.IO;
using OracleMS.ViewModels;

namespace OracleMS.Views
{
    /// <summary>
    /// FunctionEditorView.xaml 的互動邏輯
    /// </summary>
    public partial class FunctionEditorView : UserControl
    {
        private FunctionEditorViewModel? ViewModel => DataContext as FunctionEditorViewModel;

        public FunctionEditorView()
        {
            InitializeComponent();

            // 載入 PL/SQL 語法高亮定義
            LoadPlSqlSyntaxHighlighting();

            // 設定 TextEditor 的文字變更事件（在建構函式中綁定，避免重複執行）
            SourceCodeEditor.TextChanged += SourceCodeEditor_TextChanged;

            // 設定 DataContext 變更事件
            DataContextChanged += FunctionEditorView_DataContextChanged;

            // 設定快捷鍵
            SetupKeyBindings();
        }

        private void FunctionEditorView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.OldValue is FunctionEditorViewModel oldViewModel)
            {
                // 解除舊的 ViewModel 事件綁定
                oldViewModel.PropertyChanged -= ViewModel_PropertyChanged;
            }

            if (e.NewValue is FunctionEditorViewModel newViewModel)
            {
                // 綁定新的 ViewModel 事件
                newViewModel.PropertyChanged += ViewModel_PropertyChanged;

                // 初始化編輯器文字（仿照 ViewEditor 的做法）
                SourceCodeEditor.Text = newViewModel.FunctionSource ?? string.Empty;
                System.Diagnostics.Debug.WriteLine($"FunctionEditor DataContext 變更，初始化編輯器文字: 長度={newViewModel.FunctionSource?.Length ?? 0}");
            }
        }

        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(FunctionEditorViewModel.FunctionSource))
            {
                // 如果 ViewModel 的 FunctionSource 屬性變更，更新編輯器文字
                var viewModel = sender as FunctionEditorViewModel;
                if (viewModel != null && SourceCodeEditor.Text != viewModel.FunctionSource)
                {
                    System.Diagnostics.Debug.WriteLine($"FunctionEditor 更新編輯器文字: 長度={viewModel.FunctionSource?.Length ?? 0}");
                    SourceCodeEditor.Text = viewModel.FunctionSource ?? string.Empty;
                }
            }
        }

        private void SourceCodeEditor_TextChanged(object? sender, EventArgs e)
        {
            // 將編輯器文字變更同步到 ViewModel
            var viewModel = DataContext as FunctionEditorViewModel;
            if (viewModel != null && SourceCodeEditor.Text != viewModel.FunctionSource)
            {
                viewModel.FunctionSource = SourceCodeEditor.Text;
            }
        }

        private void LoadPlSqlSyntaxHighlighting()
        {
            try
            {
                // 嘗試載入內嵌的 PL/SQL 語法高亮定義
                using var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("OracleMS.Resources.PLSQL.xshd");
                if (stream != null)
                {
                    using var reader = new XmlTextReader(stream);
                    SourceCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                }
                else
                {
                    // 如果找不到內嵌資源，嘗試從檔案載入
                    var xshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "PLSQL.xshd");
                    if (File.Exists(xshdFile))
                    {
                        using var reader = new XmlTextReader(xshdFile);
                        SourceCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                    }
                    else
                    {
                        // 如果找不到 PL/SQL 定義，使用 SQL 定義作為替代
                        var sqlXshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "SQL.xshd");
                        if (File.Exists(sqlXshdFile))
                        {
                            using var reader = new XmlTextReader(sqlXshdFile);
                            SourceCodeEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 載入語法高亮定義失敗，記錄錯誤但不中斷程式
                System.Diagnostics.Debug.WriteLine($"載入 PL/SQL 語法高亮定義失敗: {ex.Message}");
            }
        }

        private void SetupKeyBindings()
        {
            // 設定 Ctrl+S 快捷鍵
            var saveBinding = new KeyBinding
            {
                Key = Key.S,
                Modifiers = ModifierKeys.Control,
                Command = ApplicationCommands.Save
            };
            
            this.InputBindings.Add(saveBinding);
            
            // 設定 F5 快捷鍵編譯
            var compileBinding = new KeyBinding
            {
                Key = Key.F5,
                Command = new RelayCommand(() => ViewModel?.CompileCommand.Execute(null))
            };
            
            this.InputBindings.Add(compileBinding);
            
            // 設定 Ctrl+F5 快捷鍵驗證語法
            var validateBinding = new KeyBinding
            {
                Key = Key.F5,
                Modifiers = ModifierKeys.Control,
                Command = new RelayCommand(() => ViewModel?.ValidateCommand.Execute(null))
            };
            
            this.InputBindings.Add(validateBinding);
            
            // 設定 F9 快捷鍵執行測試
            var executeBinding = new KeyBinding
            {
                Key = Key.F9,
                Command = new RelayCommand(() => ViewModel?.ExecuteTestCommand.Execute(null))
            };
            
            this.InputBindings.Add(executeBinding);
            
            // 設定命令綁定
            this.CommandBindings.Add(new CommandBinding(ApplicationCommands.Save, (s, e) => 
            {
                if (ViewModel != null && ViewModel.SaveCommand.CanExecute(null))
                {
                    ViewModel.SaveCommand.Execute(null);
                }
            }));
        }
    }

    // 簡單的 RelayCommand 實作
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();
    }
}