using System.Data;
using System.Threading.Tasks;
using OracleMS.Models;

namespace OracleMS.Interfaces
{
    /// <summary>
    /// 資料庫物件編輯服務介面
    /// </summary>
    public interface IObjectEditorService
    {
        // Table operations
        /// <summary>
        /// 取得資料表定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="tableName">資料表名稱</param>
        /// <returns>資料表定義</returns>
        Task<TableDefinition> GetTableDefinitionAsync(IDbConnection connection, string tableName);

        /// <summary>
        /// 儲存資料表定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">資料表定義</param>
        /// <returns>非同步工作</returns>
        Task SaveTableDefinitionAsync(IDbConnection connection, TableDefinition definition);
        
        // View operations
        /// <summary>
        /// 取得檢視表定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="viewName">檢視表名稱</param>
        /// <returns>檢視表 SQL 定義</returns>
        Task<string> GetViewDefinitionAsync(IDbConnection connection, string viewName);

        /// <summary>
        /// 儲存檢視表定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="viewName">檢視表名稱</param>
        /// <param name="definition">檢視表 SQL 定義</param>
        /// <returns>非同步工作</returns>
        Task SaveViewDefinitionAsync(IDbConnection connection, string viewName, string definition);
        
        // Procedure/Function operations
        /// <summary>
        /// 取得預存程序原始碼
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="procedureName">預存程序名稱</param>
        /// <returns>預存程序原始碼</returns>
        Task<string> GetProcedureSourceAsync(IDbConnection connection, string procedureName);

        /// <summary>
        /// 取得函數原始碼
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="functionName">函數名稱</param>
        /// <returns>函數原始碼</returns>
        Task<string> GetFunctionSourceAsync(IDbConnection connection, string functionName);

        /// <summary>
        /// 編譯預存程序
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="procedureName">預存程序名稱</param>
        /// <param name="source">預存程序原始碼</param>
        /// <returns>編譯結果</returns>
        Task<CompilationResult> CompileProcedureAsync(IDbConnection connection, string procedureName, string source);

        /// <summary>
        /// 編譯函數
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="functionName">函數名稱</param>
        /// <param name="source">函數原始碼</param>
        /// <returns>編譯結果</returns>
        Task<CompilationResult> CompileFunctionAsync(IDbConnection connection, string functionName, string source);
        
        // Package operations
        /// <summary>
        /// 取得套件定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="packageName">套件名稱</param>
        /// <returns>套件定義</returns>
        Task<PackageDefinition> GetPackageDefinitionAsync(IDbConnection connection, string packageName);

        /// <summary>
        /// 編譯套件
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="packageName">套件名稱</param>
        /// <param name="spec">套件規格</param>
        /// <param name="body">套件主體</param>
        /// <returns>編譯結果</returns>
        Task<CompilationResult> CompilePackageAsync(IDbConnection connection, string packageName, string spec, string body);
        
        // Sequence operations
        /// <summary>
        /// 取得序列定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="sequenceName">序列名稱</param>
        /// <returns>序列定義</returns>
        Task<SequenceDefinition> GetSequenceDefinitionAsync(IDbConnection connection, string sequenceName);

        /// <summary>
        /// 儲存序列定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">序列定義</param>
        /// <returns>非同步工作</returns>
        Task SaveSequenceDefinitionAsync(IDbConnection connection, SequenceDefinition definition);
        
        // Trigger operations
        /// <summary>
        /// 取得觸發器定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="triggerName">觸發器名稱</param>
        /// <returns>觸發器定義</returns>
        Task<TriggerDefinition> GetTriggerDefinitionAsync(IDbConnection connection, string triggerName);

        /// <summary>
        /// 儲存觸發器定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">觸發器定義</param>
        /// <returns>非同步工作</returns>
        Task<CompilationResult> SaveTriggerDefinitionAsync(IDbConnection connection, TriggerDefinition definition);
        
        // Index operations
        /// <summary>
        /// 取得索引定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <returns>索引定義</returns>
        Task<IndexDefinition> GetIndexDefinitionAsync(IDbConnection connection, string indexName);

        /// <summary>
        /// 重建索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <returns>非同步工作</returns>
        Task RebuildIndexAsync(IDbConnection connection, string indexName);
    }

    /// <summary>
    /// 編譯結果
    /// </summary>
    public class CompilationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 錯誤行號
        /// </summary>
        public int? ErrorLine { get; set; }

        /// <summary>
        /// 錯誤位置
        /// </summary>
        public int? ErrorPosition { get; set; }

        /// <summary>
        /// 錯誤代碼
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// 建立成功的編譯結果
        /// </summary>
        /// <returns>編譯結果</returns>
        public static CompilationResult Success()
        {
            return new CompilationResult { IsSuccess = true };
        }

        /// <summary>
        /// 建立失敗的編譯結果
        /// </summary>
        /// <param name="errorMessage">錯誤訊息</param>
        /// <param name="errorCode">錯誤代碼</param>
        /// <param name="errorLine">錯誤行號</param>
        /// <param name="errorPosition">錯誤位置</param>
        /// <returns>編譯結果</returns>
        public static CompilationResult Failure(string errorMessage, string errorCode = "", int? errorLine = null, int? errorPosition = null)
        {
            return new CompilationResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorCode = errorCode,
                ErrorLine = errorLine,
                ErrorPosition = errorPosition
            };
        }
    }
}